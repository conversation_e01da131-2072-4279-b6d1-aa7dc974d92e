import hashlib
import io
import logging
from datetime import datetime, timedelta, timezone
from typing import Any

from uuid_extensions import uuid7

from app.database.queries import check_book_duplicates
from app.processing.anthology_detector import AnthologyDetector
from app.processing.artifact_saver import save_artifact
from app.processing.book_data_builder import Book<PERSON>ata<PERSON>uilder
from app.processing.book_validator import Book<PERSON>ali<PERSON><PERSON>
from app.processing.canonical_model import CanonicalBook
from app.processing.database_saver import DatabaseSaver
from app.processing.date_extractor import (
    extract_best_date,
)
from app.processing.error_handler import (
    ProcessingError,
    QuarantineError,
    QuarantineType,
)
from app.processing.fragment_detector import FragmentDetector
from app.processing.hash_computer import HashComputer
from app.processing.parser_dispatcher import ParserDispatcher
from app.processing.pruner import prune_book
from app.processing.queue_manager import TaskQueueManager
from app.processing.small_book_detector import SmallBookDetector
from app.storage.base import StorageManager


class BookProcessor:
    """Высокоуровневый процессор для обработки книг.

    Инкапсулирует всю бизнес-логику обработки книги из воркера
    """

    def __init__(self, storage_manager: StorageManager):
        self.logger = logging.getLogger(__name__)

        # Dependency Injection для StorageManager
        self.storage_manager = storage_manager

        # Инициализация компонентов обработки
        self.parser_dispatcher = ParserDispatcher()
        self.hash_computer = HashComputer()
        self.database_saver = DatabaseSaver()
        self.book_data_builder = BookDataBuilder()
        self.queue_manager = TaskQueueManager()

        # Детекторы качества книг
        self.fragment_detector = FragmentDetector()  # Детектор фрагментов
        self.small_book_detector = SmallBookDetector()  # Детектор маленьких книг
        self.anthology_detector = AnthologyDetector()  # Детектор антологий

        # Централизованный валидатор книг
        self.book_validator = BookValidator(
            fragment_detector=self.fragment_detector,
            small_book_detector=self.small_book_detector,
            anthology_detector=self.anthology_detector,
        )

    def process(self, task_data: dict[str, Any]) -> dict[str, Any]:
        """Выполняет полную обработку книги.

        Args:
            task_data: Данные задачи из очереди с полями:
                - source_type: int
                - source_id: int
                - archive_path: str
                - book_filename: str
                - archive_mtime: float

        Returns:
            Результат обработки с информацией о выполненных этапах

        Raises:
            QuarantineError: При ошибках бизнес-логики
            Exception: При критических системных ошибках

        """
        try:
            archive_path = task_data["archive_path"]
            book_filename = task_data["book_filename"]

            self.logger.debug(f"📖 Начинаем обработку: {book_filename} из {archive_path}")

            # Этап 0: Извлечение source_id и source_type из задачи
            source_id = task_data.get("source_id")
            source_type = task_data.get("source_type")

            # Этап 1: Извлечение содержимого из архива
            book_stream = self._extract_content(task_data)

            # Этап 2: Парсинг в каноническую модель
            canonical_book = self._parse_to_canonical(book_stream, task_data)

            # Установка source_info в каноническую модель
            canonical_book.source_id = source_id
            canonical_book.source_type = source_type

            # Этап 2.6: Мониторинг сломанных сносок (только логирование)
            self._log_broken_footnotes_monitoring()

            # Этап 2.5: Проверка на ознакомительный фрагмент
            self._check_fragment(canonical_book)

            # Этап 3: Постобработка канонической модели
            book_id = self._postprocess_canonical_book(canonical_book, task_data)

            # Этап 4: Проверка дубликатов
            duplicate_result = self._check_duplicates(canonical_book)
            if duplicate_result["should_skip"]:
                return {
                    "status": "skipped",
                    "reason": "duplicate",
                }

            # Этап 5: Сохранение в БД и очередь RAG
            self._save_and_enqueue(canonical_book, task_data, book_id, duplicate_result["hashes"])

            self.logger.info(f"✅ Книга успешно обработана: {canonical_book.title} (ID: {book_id})")

            return {
                "status": "success",
                "book_id": book_id,
                "title": canonical_book.title,
            }

        except Exception:
            raise

    def _extract_content(self, task_data: dict[str, Any]) -> io.BytesIO:
        """Извлекает содержимое файла книги из архива через StorageManager."""
        archive_path = task_data["archive_path"]
        book_filename = task_data["book_filename"]

        try:
            book_stream = self.storage_manager.read_file_from_archive(archive_path, book_filename)
            self.logger.debug(f"Файл {book_filename} извлечен из архива {archive_path}")
            return book_stream
        except Exception as e:
            raise QuarantineError(f"Ошибка извлечения файла {book_filename} из архива {archive_path}: {e}") from e

    def _parse_to_canonical(self, book_stream: io.BytesIO, task_data: dict[str, Any]):
        """Парсит поток книги в каноническую модель."""
        book_filename = task_data["book_filename"]
        archive_mtime = task_data["archive_mtime"]

        canonical_book = self.parser_dispatcher.parse_to_canonical(
            source=book_stream, source_filename=book_filename, file_mtime=archive_mtime
        )
        self.logger.debug(f"Книга распарсена: {canonical_book.title}")
        return canonical_book

    def _log_broken_footnotes_monitoring(self) -> None:
        """Логирует информацию о сломанных сносках для аналитики без отправки в карантин.

        Этот метод предназначен только для мониторинга и сбора аналитики.
        В продакшене broken_footnotes НЕ являются критической ошибкой.
        """
        # Проверяем только если есть доступ к трансформеру и он обработал сноски
        if hasattr(self.parser_dispatcher, "_last_transformer") and self.parser_dispatcher._last_transformer:
            transformer = self.parser_dispatcher._last_transformer
            if hasattr(transformer, "has_broken_footnotes") and transformer.has_broken_footnotes():
                broken_footnotes = transformer.get_broken_footnotes()
                broken_count = len(broken_footnotes)
                broken_ids = ", ".join(broken_footnotes[:5])  # Показываем первые 5 ID

                # Логируем как INFO для аналитики, НЕ как WARNING/ERROR
                self.logger.info(
                    f"📊 МОНИТОРИНГ: Обнаружены недоступные сноски ({broken_count} шт.): {broken_ids}. "
                    f"Это НЕ критическая ошибка - информация для анализа эвристик извлечения."
                )

    def _determine_quarantine_type(self, canonical_book: CanonicalBook, fb2_transformer=None) -> tuple[QuarantineType, str] | None:
        """Определяет тип карантина для книги на основе централизованного валидатора.

        Args:
            canonical_book: Каноническая модель книги
            fb2_transformer: FB2 трансформер для проверки сносок (опционально)

        Returns:
            Кортеж (тип_карантина, причина) или None если книга прошла все проверки
        """
        # Используем централизованный валидатор для получения первой блокирующей аномалии
        first_anomaly = self.book_validator.get_first_blocking_anomaly(canonical_book, fb2_transformer)

        if not first_anomaly:
            return None  # Книга прошла все проверки

        anomaly_type = first_anomaly["type"]
        reason = first_anomaly["reason"]

        # Преобразуем строковый тип аномалии в QuarantineType
        if anomaly_type == "trial_fragments":
            # Дополняем причину информацией об авторе для фрагментов
            author_info = self._format_author_info(canonical_book)
            enhanced_reason = (
                f"Обнаружен ознакомительный фрагмент: '{canonical_book.title}' "
                f"от {author_info}. Фрагменты нарушают дедупликацию и блокируют "
                f"загрузку полных версий книг. Детали: {reason}"
            )
            return QuarantineType.TRIAL, enhanced_reason
        elif anomaly_type == "small_content":
            return QuarantineType.SMALL_CONTENT, reason
        elif anomaly_type == "few_chapters":
            return QuarantineType.FEW_CHAPTERS, reason
        elif anomaly_type == "anthology_books":
            return QuarantineType.ANTHOLOGIES, reason
        elif anomaly_type == "broken_footnotes":
            return QuarantineType.FOOTNOTES, reason
        else:
            # Неизвестный тип аномалии - логируем и возвращаем как ERROR
            self.logger.warning(f"Неизвестный тип аномалии: {anomaly_type}")
            return QuarantineType.ERROR, f"Неизвестная аномалия: {reason}"

    def _format_author_info(self, canonical_book: CanonicalBook) -> str:
        """Форматирует информацию об авторах книги."""
        if not canonical_book.authors:
            return "Неизвестный автор"

        authors_list = []
        for author in canonical_book.authors:
            name_parts = [
                author.first_name,
                author.middle_name,
                author.last_name,
            ]
            full_name = " ".join(filter(None, name_parts))
            if author.nickname:
                full_name += f" ({author.nickname})"
            authors_list.append(full_name)
        return ", ".join(authors_list)

    def _check_fragment(self, canonical_book) -> None:
        """Проверяет качество книги и определяет нужен ли карантин.

        Это комплексная проверка для предотвращения попадания некачественных книг в основную коллекцию.
        Включает проверки на фрагменты, маленькие книги и антологии.

        Raises:
            QuarantineError: Если книга должна быть помещена в карантин

        """
        # Получаем FB2 трансформер для проверки сносок (если доступен)
        fb2_transformer = None
        if hasattr(self.parser_dispatcher, "_last_transformer") and self.parser_dispatcher._last_transformer:
            fb2_transformer = self.parser_dispatcher._last_transformer

        # Определяем тип карантина с помощью централизованного валидатора
        quarantine_result = self._determine_quarantine_type(canonical_book, fb2_transformer)

        if quarantine_result:
            quarantine_type, reason = quarantine_result

            self.logger.warning(f"🚫 {reason}")

            # Генерируем типизированную QuarantineError для правильной обработки в воркере
            raise QuarantineError(
                reason,
                quarantine_type=quarantine_type,
                details={
                    "title": canonical_book.title,
                    "authors": self._format_author_info(canonical_book),
                    "chapters_count": len(canonical_book.chapters),
                    "quarantine_category": quarantine_type.value,
                },
            )

    def _postprocess_canonical_book(self, canonical_book, task_data: dict[str, Any]) -> str:
        """Выполняет постобработку канонической модели:
        - Извлечение даты и генерация ID
        - Сохранение информации о дате в модели
        - Очистка данных
        БЕЗ сохранения артефакта (перенесено в двухфазную логику)
        """
        # Генерация ID на основе лучшей даты (теперь всегда в UTC)
        archive_mtime = task_data["archive_mtime"]
        best_date, date_source = extract_best_date(canonical_book.raw_source_model, archive_mtime)

        # ==================================================================================
        # ОБРАБОТКА ДАТ ДО 1970 ГОДА ДЛЯ UUID v7
        # ==================================================================================
        #
        # UUID v7 по спецификации RFC 9562 использует 48-битный Unix timestamp в миллисекундах,
        # который по определению не может быть отрицательным (до 1 января 1970 00:00:00 UTC).
        #
        # ПРОБЛЕМА: Многие книги имеют даты публикации до 1970 года (например, классическая
        # литература), что делает невозможным прямое использование их дат для UUID v7.
        #
        # РЕШЕНИЕ: Для книг с датами до 1970 года создается "синтетическая дата" на основе
        # криптографического хеша метаданных книги, размещенная в диапазоне 1970-1980 гг.
        #
        # ПРЕИМУЩЕСТВА ДАННОГО ПОДХОДА:
        # 1. Архитектурная консистентность - все книги используют UUID v7
        # 2. Извлекаемость даты - из UUID всегда можно получить timestamp
        # 3. Уникальность - разные книги получают разные синтетические даты
        # 4. Стабильность - одна книга всегда получает одну и ту же синтетическую дату
        # 5. Сохранение реальной даты - оригинальная дата сохраняется в метаданных
        #
        # АЛЬТЕРНАТИВЫ И ИХ НЕДОСТАТКИ:
        # - UUID v4 (случайный): Теряется возможность извлечения даты из ID
        # - Фиксированная дата 1970-01-01: Нарушается уникальность ID для разных книг
        # - Смешанные типы UUID: Усложняется архитектура и логика обработки
        #
        if best_date.timestamp() < 0:
            # Создаем уникальную подпись книги из title + authors + source_id
            book_signature = f"{canonical_book.title}_{len(canonical_book.authors)}_{canonical_book.source_id}"
            hash_value = int(
                hashlib.md5(book_signature.encode("utf-8"), usedforsecurity=False).hexdigest()[:8],
                16,
            )

            # Создаем дату в диапазоне 1970-1980 на основе хеша (10 лет после эпохи)
            epoch_start = datetime(1970, 1, 1, tzinfo=timezone.utc)
            offset_seconds = hash_value % (365 * 24 * 3600 * 10)  # 10 лет
            uuid_date = epoch_start + timedelta(seconds=offset_seconds)

            self.logger.debug(f"Дата книги {best_date} до эпохи Unix. Создана уникальная дата для UUID7: {uuid_date}")
        else:
            uuid_date = best_date

        # Преобразуем timestamp в наносекунды для UUIDv7
        timestamp_ns = int(uuid_date.timestamp() * 1e9)
        book_id = str(uuid7(ns=timestamp_ns))
        self.logger.debug(f"Сгенерирован ID книги (UUIDv7) на основе даты {uuid_date}: {book_id}")

        # Сохраняем информацию о дате в модели - теперь в формате ISO с UTC
        canonical_book.book_id_generation_date = best_date.isoformat()

        # Используем источник даты полученный из extract_best_date
        canonical_book.book_id_date_source = date_source
        self.logger.debug(f"Источник даты для book_id: {date_source}")

        # Очистка канонической модели
        prune_book(canonical_book)
        self.logger.debug("Каноническая модель очищена")

        return book_id

    def _check_duplicates(self, canonical_book) -> dict[str, Any]:
        """Проверяет книгу на дубликаты и принимает бизнес-решения.

        Returns:
            Dict с результатом проверки и хэшами:
            {
                "should_skip": bool,
                "hashes": dict
            }
        """
        try:
            hashes = self.hash_computer.compute_hashes(canonical_book)
            duplicate_db_record = check_book_duplicates(hashes["metadata_hash"])

            should_skip = duplicate_db_record is not None
            if should_skip:
                self.logger.info(
                    f"Дубликат книги '{canonical_book.title}' пропущен. ID существующей записи: {duplicate_db_record.get('existing_book_id') if duplicate_db_record else 'unknown'}"
                )

            return {"hashes": hashes, "should_skip": should_skip}

        except Exception as e:
            # NB: Используем именно ProcessingError (RETRY).
            # Несмотря на то, что ErrorHandler помечает такую ошибку как «retry»,
            # текущая реализация BookWorker **не** возвращает задачу в очередь,
            # а сразу отправляет файл в карантин и финализирует задачу.
            # Поэтому бесконечных повторных попыток не возникает.
            # Если в будущем появится логика `return_task_to_queue`, этот участок
            # потребуется пересмотреть (возможно, заменить на FatalError либо
            # явный возврат задачи).
            self.logger.error(f"Ошибка проверки дубликатов: {e}")
            # В случае ошибки БД лучше не сохранять книгу, чтобы избежать дублей
            raise ProcessingError(f"Ошибка доступа к БД при проверке дубликатов: {e}") from e

    def _save_and_enqueue(
        self,
        canonical_book,
        task_data: dict[str, Any],
        book_id: str,
        hashes: dict[str, str],
    ):
        """Двухфазное сохранение книги для обеспечения атомарности данных.

        Фаза 1: Сохранение метаданных в БД (process_status=10)
        Фаза 2: Создание артефакта + обновление статуса (process_status=20)

        Это гарантирует целостность между БД и файловой системой.
        """
        # Подготавливаем DTO для сохранения
        book_dto = self.book_data_builder.build_book_dto_from_canonical(
            canonical_book,
            task_data,
            hashes["metadata_hash"],
        )

        # ФАЗА 1: Сохраняем метаданные в БД со статусом 10 (метаданные сохранены)
        self.database_saver.save_book_metadata_only(book_dto, book_id)
        self.logger.debug(f"Фаза 1: Метаданные книги сохранены в БД (статус 10): {book_id}")

        # ФАЗА 2: Создаем артефакт на диске
        artifact_path = save_artifact(canonical_book, book_id)
        self.logger.debug(f"Фаза 2: Каноническая модель сохранена: {artifact_path}")

        # ФАЗА 2: Обновляем статус на полностью обработано (20)
        self.database_saver.update_book_status(book_id, 20)
        self.logger.debug(f"Фаза 2: Статус книги обновлен на 20 (полностью обработано): {book_id}")

        # Ставим задачу в очередь для RAG-пайплайна
        self.queue_manager.enqueue_rag_task(book_id)
        self.logger.debug(f"Книга поставлена в очередь RAG: {book_id}")
