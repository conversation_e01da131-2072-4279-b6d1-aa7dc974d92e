# app/processing/book_validator.py

"""Централизованный валидатор книг для обнаружения всех типов аномалий.

Этот модуль инкапсулирует всю логику проверки книги на аномалии,
используемую как в основном пайплайне (BookProcessor), так и в
инструментах анализа (run_anomaly_analyzer.py).
"""

import logging
from typing import Optional

from app.processing.anthology_detector import AnthologyDetector
from app.processing.canonical_model import CanonicalBook
from app.processing.fragment_detector import FragmentDetector
from app.processing.parsers.fb2.fb2_transformer import FB2Transformer
from app.processing.small_book_detector import SmallBookDetector

logger = logging.getLogger(__name__)


class BookValidator:
    """Централизованный валидатор для обнаружения всех типов аномалий в книгах.
    
    Инкапсулирует логику всех детекторов и предоставляет единый интерфейс
    для проверки книг на аномалии. Используется как в основном пайплайне,
    так и в инструментах анализа.
    """

    def __init__(
        self,
        fragment_detector: Optional[FragmentDetector] = None,
        small_book_detector: Optional[SmallBookDetector] = None,
        anthology_detector: Optional[AnthologyDetector] = None,
    ):
        """Инициализирует валидатор с детекторами аномалий.
        
        Args:
            fragment_detector: Детектор ознакомительных фрагментов
            small_book_detector: Детектор структурных аномалий
            anthology_detector: Детектор антологий/сборников
        """
        self.fragment_detector = fragment_detector or FragmentDetector()
        self.small_book_detector = small_book_detector or SmallBookDetector()
        self.anthology_detector = anthology_detector or AnthologyDetector()

    def validate(
        self,
        canonical_book: CanonicalBook,
        fb2_transformer: Optional[FB2Transformer] = None,
    ) -> list[dict[str, str]]:
        """Проверяет книгу на все типы аномалий.
        
        Выполняет последовательную проверку книги всеми детекторами
        и возвращает список всех найденных аномалий с причинами.
        
        Args:
            canonical_book: Каноническая модель книги для проверки
            fb2_transformer: FB2 трансформер для проверки сносок (опционально)
            
        Returns:
            Список словарей с найденными аномалиями в формате:
            [{"type": "trial_fragments", "reason": "..."}, ...]
            Пустой список если аномалий не найдено.
        """
        anomalies = []

        # Проверка 1: Ознакомительные фрагменты (приоритет 1)
        if self.fragment_detector.is_fragment(canonical_book):
            reason = self.fragment_detector.get_fragment_reason(canonical_book)
            anomalies.append({
                "type": "trial_fragments",
                "reason": reason
            })

        # Проверка 2: Структурные аномалии (приоритет 2)
        quarantine_type = self.small_book_detector.check_book_structure(canonical_book)
        if quarantine_type:
            reason = self.small_book_detector.get_rejection_reason(canonical_book)
            
            # Преобразуем QuarantineType в строковый тип аномалии
            if quarantine_type.value == "small_content":
                anomalies.append({
                    "type": "small_content",
                    "reason": reason
                })
            elif quarantine_type.value == "few_chapters":
                anomalies.append({
                    "type": "few_chapters", 
                    "reason": reason
                })

        # Проверка 3: Антологии/сборники (приоритет 3)
        if self.anthology_detector.is_anthology(canonical_book):
            reason = self.anthology_detector.get_anthology_reason(canonical_book)
            anomalies.append({
                "type": "anthology_books",
                "reason": reason
            })

        # Проверка 4: Сломанные сноски (если доступен трансформер)
        if fb2_transformer and self._check_footnotes_anomaly(fb2_transformer):
            anomalies.append({
                "type": "broken_footnotes",
                "reason": "Обнаружены нераспарсенные сноски в FB2 файле"
            })

        logger.debug(f"Валидация завершена: найдено {len(anomalies)} аномалий")
        return anomalies

    def get_first_blocking_anomaly(
        self,
        canonical_book: CanonicalBook,
        fb2_transformer: Optional[FB2Transformer] = None,
    ) -> Optional[dict[str, str]]:
        """Возвращает первую блокирующую аномалию для основного пайплайна.
        
        Используется в BookProcessor для определения типа карантина.
        Возвращает только первую найденную аномалию согласно приоритету.
        
        Args:
            canonical_book: Каноническая модель книги для проверки
            fb2_transformer: FB2 трансформер для проверки сносок (опционально)
            
        Returns:
            Словарь с первой найденной аномалией или None если аномалий нет
        """
        anomalies = self.validate(canonical_book, fb2_transformer)
        return anomalies[0] if anomalies else None

    def _check_footnotes_anomaly(self, fb2_transformer: FB2Transformer) -> bool:
        """Проверяет наличие проблем со сносками в FB2 трансформере.
        
        Args:
            fb2_transformer: FB2 трансформер для анализа
            
        Returns:
            True если обнаружены проблемы со сносками
        """
        # Проверяем наличие нераспарсенных сносок
        # Это упрощенная проверка - в реальности может быть более сложная логика
        if hasattr(fb2_transformer, 'unparsed_footnotes'):
            return len(fb2_transformer.unparsed_footnotes) > 0
        
        # Если атрибут отсутствует, считаем что проблем нет
        return False
