# Инструменты для анализа и диагностики

Этот каталог содержит специализированные инструменты для анализа, диагностики и тюнинга системы обработки книг.

## 🔍 Анализ аномалий

### `run_anomaly_analyzer.py` - Профессиональный инструмент для тюнинга детекторов

Основной инструмент для итеративного улучшения детекторов аномалий с тремя режимами работы:

**Режимы работы:**
- **Разведка** (`--path`): Сканирует новые файлы по указанным путям
- **Тюнинг** (по умолчанию): Перепроверяет файлы из `anomalies_registry.json`
- **Исправление** (`--fix`): Применяет исправления для книг из карантина

**Примеры использования:**
```bash
# Тюнинг всех аномалий
python tools/run_anomaly_analyzer.py

# Тюнинг только trial аномалий
python tools/run_anomaly_analyzer.py --type trial

# Разведка новых файлов
python tools/run_anomaly_analyzer.py --path /data/books

# Исправление карантинных книг
python tools/run_anomaly_analyzer.py --fix

# Исправление без подтверждения
python tools/run_anomaly_analyzer.py --fix --yes
```

**Основной артефакт:** `anomalies_registry.json` - персистентный файл состояния аномалий.

### `run_statistical_reporter.py` - Генератор статистических отчетов

Специализированный инструмент для создания CSV-отчетов с метаданными книг без запуска детекторов аномалий.

**Особенности:**
- Облегченный парсинг: только статистические поля
- Не запускает BookValidator (детекторы аномалий)
- Многопоточная обработка архивов
- Создает CSV отчет с метаданными книг

**Примеры использования:**
```bash
# Создание отчета по директории
python tools/run_statistical_reporter.py --path /data/books --output report.csv

# Анализ конкретных архивов
python tools/run_statistical_reporter.py --path /archive1.zip /archive2.zip --output analysis.csv

# Настройка количества потоков
python tools/run_statistical_reporter.py --path /books --output stats.csv --workers 20
```

### `old_anomaly_analyzer.py` - Устаревший анализатор (legacy)

⚠️ **Устаревший инструмент** - заменен на `run_anomaly_analyzer.py` и `run_statistical_reporter.py`.

Сохранен для обратной совместимости и справочных целей.

## 📊 Анализ карантина

### `analyze_quarantine.py` - Анализ карантинных книг

Инструмент для анализа и управления книгами в карантине PostgreSQL.

**Возможности:**
- Поиск записей по типу карантина, архиву, причине
- Статистика карантина
- Экспорт в CSV
- Чтение файлов из карантина

**Примеры использования:**
```bash
# Статистика карантина
python tools/analyze_quarantine.py --stats

# Поиск trial аномалий
python tools/analyze_quarantine.py --type trial --limit 10

# Поиск по архиву
python tools/analyze_quarantine.py --archive-path "problematic.zip"

# Экспорт в CSV
python tools/analyze_quarantine.py --stats --export-csv quarantine_report.csv
```

## 🔧 Диагностика и отладка

### `run_102_analize_single_file__debug_pipeline_20.py` - Отладка пайплайна

Инструмент для отладки обработки одного файла с полной диагностикой пайплайна.

**Особенности:**
- Имитирует полный пайплайн обработки
- Не изменяет исходные файлы
- Не пишет в БД и Redis
- Создает детальный диагностический отчет

**Использование:**
```bash
python tools/run_102_analize_single_file__debug_pipeline_20.py \
    --input-file /path/to/book.fb2 \
    --output-file diagnostic_report.json \
    --debug
```

## 🔴 Redis инструменты

### `redis_diagnostic.py` - Диагностика Redis

Комплексный анализ состояния Redis очередей и данных.

### `redis_monitor.py` - Мониторинг Redis

Мониторинг Redis в реальном времени с метриками производительности.

### `redis_dashboard.py` - Дашборд Redis

Веб-дашборд для визуализации состояния Redis очередей.

**Подробная документация:** [README_redis_tools.md](README_redis_tools.md)

## 🔄 Восстановление системы

### `test_recovery.py` - Тестирование восстановления

Инструмент для тестирования процедур восстановления системы.

**Подробная документация:** [README_test_recovery.md](README_test_recovery.md)

## 📚 Вспомогательные модули

### `analysis/` - Модули анализа аномалий

- `detector.py` - Детектор аномалий с поддержкой карантина
- `registry.py` - Управление JSON реестром аномалий
- `mode.py` - Определение режима работы анализатора
- `types.py` - Общие типы данных для анализа

### `utils.py` - Утилитарные функции

Общие утилиты для работы с архивами, парсинга книг и обработки файлов.

**Подробная документация:** [README_utils.md](README_utils.md)

## 🚀 Быстрый старт

1. **Анализ аномалий:**
   ```bash
   # Первичная синхронизация с карантином
   python tools/run_anomaly_analyzer.py
   
   # Создание статистического отчета
   python tools/run_statistical_reporter.py --path /data/books --output report.csv
   ```

2. **Диагностика системы:**
   ```bash
   # Проверка Redis
   python tools/redis_diagnostic.py
   
   # Анализ карантина
   python tools/analyze_quarantine.py --stats
   ```

3. **Отладка проблем:**
   ```bash
   # Отладка конкретного файла
   python tools/run_102_analize_single_file__debug_pipeline_20.py \
       --input-file problematic.fb2 --output-file debug.json
   ```

## 📋 Требования

- Python 3.12+
- Доступ к PostgreSQL (для карантина)
- Доступ к Redis (для очередей)
- Установленные зависимости проекта

**Примечание:** Некоторые инструменты могут работать в ограниченном режиме при отсутствии части зависимостей.

## 🔗 Связанная документация

- [Архитектура pipeline](../doc/operations/pipeline01.md)
- [Redis очереди](../doc/redis_queues.md)
- [Система карантина](../doc/operations/quarantine.md)
- [База данных](../doc/database/schema.md)
