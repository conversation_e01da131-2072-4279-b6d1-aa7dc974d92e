#!/usr/bin/env python3
"""
Профессиональный инструмент для тюнинга детекторов аномалий.

Этот инструмент предназначен для разработчика/QA и обеспечивает итеративное
улучшение детекторов аномалий через три режима работы:

1. **Разведка** (--path): Сканирует новые файлы по указанным путям
2. **Тюнинг** (по умолчанию): Перепроверяет файлы из anomalies_registry.json
3. **Исправление** (--fix): Применяет исправления для книг из карантина

Основной артефакт: anomalies_registry.json - персистентный файл состояния.

Принципы:
- Разделение задач: четкое разделение режимов работы
- Единый источник логики: все проверки через BookValidator
- Безопасность по умолчанию: изменяющие операции требуют флагов
- Максимальное переиспользование: использует существующие компоненты
"""

import argparse
import io
import logging
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Any, Optional

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Настройка базового логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%H:%M:%S')
logger = logging.getLogger(__name__)

from app.database.queries import delete_from_quarantine, get_all_quarantined_books
from tools.analysis.registry import AnomalyPathRegistry

# Импорты для полной функциональности (могут отсутствовать в тестовой среде)
try:
    from app.processing.book_validator import BookValidator
    from app.processing.parser_dispatcher import ParserDispatcher
    from app.processing.queue_manager import TaskQueueManager
    from app.storage import LocalStorageManager
    from tools.utils import get_canonical_book_from_stream, get_fb2_transformer_from_parser
    FULL_FUNCTIONALITY = True
except ImportError as e:
    logger.warning(f"⚠️ Некоторые компоненты недоступны: {e}")
    logger.warning("🔧 Работает только режим первичной синхронизации")
    FULL_FUNCTIONALITY = False


class AnomalyOrchestrator:
    """Основной класс-оркестратор для анализа аномалий.
    
    Инкапсулирует всю логику определения режима работы и выполнения
    соответствующих операций с максимальным переиспользованием
    существующих компонентов проекта.
    """

    def __init__(self):
        """Инициализирует оркестратор с необходимыми компонентами."""
        # Реестр аномалий (всегда доступен)
        self.registry_path = Path(__file__).parent / "anomalies_registry.json"
        self.anomaly_registry = AnomalyPathRegistry(self.registry_path)

        # Основные компоненты (только если доступны)
        if FULL_FUNCTIONALITY:
            self.parser_dispatcher = ParserDispatcher()
            self.book_validator = BookValidator()
            self.storage_manager = LocalStorageManager()
            self.queue_manager = TaskQueueManager()
        else:
            self.parser_dispatcher = None
            self.book_validator = None
            self.storage_manager = None
            self.queue_manager = None

        # Блокировки для потокобезопасности
        self.registry_lock = threading.Lock()
        self.stats_lock = threading.Lock()

        # Статистика
        self.stats = {
            "processed_files": 0,
            "anomalies_found": 0,
            "anomalies_fixed": 0,
            "errors": 0
        }

    def run(self, args: argparse.Namespace) -> int:
        """Главная точка входа - определяет режим и запускает соответствующую логику.
        
        Args:
            args: Аргументы командной строки
            
        Returns:
            Код возврата (0 = успех, 1 = ошибка)
        """
        try:
            # Определяем режим работы
            if args.path or args.from_file:
                return self._run_exploration_mode(args)
            elif args.fix:
                return self._run_fix_mode(args)
            else:
                return self._run_tuning_mode(args)
                
        except KeyboardInterrupt:
            logger.info("🛑 Прервано пользователем")
            return 1
        except Exception as e:
            logger.error(f"❌ Критическая ошибка: {e}")
            return 1

    def _run_exploration_mode(self, args: argparse.Namespace) -> int:
        """Режим Разведка: сканирует новые файлы по указанным путям.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата
        """
        if not FULL_FUNCTIONALITY:
            logger.error("❌ Режим Разведка требует полной функциональности")
            return 1

        logger.info("🔍 Режим: Разведка - сканирование новых файлов")
        
        # Собираем пути для сканирования
        paths_to_scan = []
        
        if args.path:
            paths_to_scan.extend(args.path)
            
        if args.from_file:
            try:
                with open(args.from_file, 'r', encoding='utf-8') as f:
                    file_paths = [line.strip() for line in f if line.strip()]
                    paths_to_scan.extend(file_paths)
            except FileNotFoundError:
                logger.error(f"❌ Файл не найден: {args.from_file}")
                return 1
        
        if not paths_to_scan:
            logger.error("❌ Не указаны пути для сканирования")
            return 1
            
        # Загружаем реестр для получения excluded_files
        self.anomaly_registry.load_registry()
        
        logger.info(f"📁 Сканирование {len(paths_to_scan)} путей...")

        # Собираем архивы для сканирования
        archives_to_scan = []
        for path_str in paths_to_scan:
            path = Path(path_str)
            if path.is_file() and path.suffix.lower() == '.zip':
                archives_to_scan.append(path)
            elif path.is_dir():
                # Ищем ZIP архивы в директории
                zip_files = list(path.glob("*.zip"))
                archives_to_scan.extend(zip_files)

        if not archives_to_scan:
            logger.warning("⚠️ Не найдено ZIP архивов для сканирования")
            return 0

        logger.info(f"📦 Найдено {len(archives_to_scan)} архивов для сканирования")

        # Выполняем сканирование (упрощенная версия)
        # В полной реализации здесь будет логика как в old_anomaly_analyzer.py
        logger.info("🔍 Сканирование архивов (базовая реализация)...")

        # TODO: Добавить полную логику сканирования архивов
        # Пока выводим только статистику найденных архивов

        logger.info("✅ Разведка завершена")
        logger.info("📝 Для создания CSV отчета используйте run_statistical_reporter.py")
        return 0

    def _run_tuning_mode(self, args: argparse.Namespace) -> int:
        """Режим Тюнинг: перепроверяет файлы из anomalies_registry.json.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата
        """
        logger.info("🔧 Режим: Тюнинг - перепроверка известных аномалий")

        # Первичная синхронизация не требует полной функциональности
        if not self.registry_path.exists():
            logger.info("📋 Реестр аномалий не найден, выполняем первичную синхронизацию...")
            return self._perform_initial_sync()

        # Остальная функциональность требует полных компонентов
        if not FULL_FUNCTIONALITY:
            logger.error("❌ Режим Тюнинг (перепроверка) требует полной функциональности")
            logger.info("💡 Доступна только первичная синхронизация")
            return 1
        
        # Загружаем реестр
        if not self.anomaly_registry.load_registry():
            logger.error("❌ Не удалось загрузить реестр аномалий")
            return 1
            
        # Получаем список файлов для перепроверки
        files_to_check = self._get_files_for_tuning(args.type)
        
        if not files_to_check:
            logger.info("📋 Нет файлов для перепроверки")
            return 0
            
        logger.info(f"🔍 Перепроверка {len(files_to_check)} файлов...")

        # Выполняем перепроверку файлов
        self._recheck_anomaly_files(files_to_check)

        # Сохраняем обновленный реестр
        self.anomaly_registry.save_registry()

        logger.info("✅ Тюнинг завершен")
        self._print_stats()
        return 0

    def _run_fix_mode(self, args: argparse.Namespace) -> int:
        """Режим Исправление: применяет исправления для книг из карантина.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата
        """
        if not FULL_FUNCTIONALITY:
            logger.error("❌ Режим Исправление требует полной функциональности")
            return 1

        logger.info("🔧 Режим: Исправление - анализ карантинных книг")
        
        try:
            # Получаем все книги из карантина
            quarantined_books = get_all_quarantined_books()
            
            if not quarantined_books:
                logger.info("📋 Карантин пуст - нет книг для проверки")
                return 0
                
            logger.info(f"📋 Найдено {len(quarantined_books)} книг в карантине")

            # Анализируем карантинные книги параллельно
            fixed_books = self._analyze_quarantined_books(quarantined_books)

            if not fixed_books:
                logger.info("📋 Нет книг готовых к исправлению")
                return 0

            # Показываем план действий
            logger.info(f"✅ Найдено {len(fixed_books)} книг готовых к исправлению:")
            for book in fixed_books[:10]:  # Показываем первые 10
                details = book.get("details", {})
                archive_path = details.get("archive_path", "N/A")
                book_filename = details.get("book_filename", "N/A")
                logger.info(f"  📖 {book['source_type']}:{book['source_id']} - {book_filename}")

            if len(fixed_books) > 10:
                logger.info(f"  ... и еще {len(fixed_books) - 10} книг")

            # Запрашиваем подтверждение если не указан --yes
            if not args.yes:
                response = input(f"\n🔧 Применить исправления для {len(fixed_books)} книг? [y/N]: ")
                if response.lower() not in ['y', 'yes', 'да']:
                    logger.info("🛑 Исправление отменено пользователем")
                    return 0

            # Применяем исправления
            success_count = self._apply_fixes(fixed_books)

            logger.info(f"✅ Исправление завершено: {success_count}/{len(fixed_books)} книг")
            return 0
            
        except Exception as e:
            logger.error(f"❌ Ошибка при работе с карантином: {e}")
            return 1

    def _perform_initial_sync(self) -> int:
        """Выполняет первичную синхронизацию реестра с карантином PostgreSQL.
        
        Returns:
            Код возврата
        """
        try:
            logger.info("🔄 Синхронизация с карантином PostgreSQL...")
            
            # Получаем все книги из карантина
            quarantined_books = get_all_quarantined_books()
            
            # Очищаем и пересоздаем реестр
            self.anomaly_registry.clear_and_rebuild()
            
            # Добавляем аномалии в реестр
            for book in quarantined_books:
                details = book.get("details", {})
                archive_path = details.get("archive_path", "")
                book_filename = details.get("book_filename", "")
                quarantine_type = book["primary_quarantine_type"]
                
                if archive_path and book_filename:
                    self.anomaly_registry.add_anomaly_path(
                        quarantine_type, archive_path, book_filename
                    )
            
            # Сохраняем реестр
            self.anomaly_registry.save_registry()
            
            logger.info(f"✅ Синхронизация завершена: {len(quarantined_books)} записей")
            logger.info(f"📋 Реестр сохранен: {self.registry_path}")
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Ошибка синхронизации: {e}")
            return 1

    def _get_files_for_tuning(self, anomaly_type_filter: Optional[str]) -> list[tuple[str, str]]:
        """Получает список файлов для перепроверки в режиме тюнинга.
        
        Args:
            anomaly_type_filter: Фильтр по типу аномалии (опционально)
            
        Returns:
            Список кортежей (archive_path, fb2_filename)
        """
        files_to_check = []
        anomalies = self.anomaly_registry.registry_data.get("anomalies", {})
        
        for anomaly_type, file_paths in anomalies.items():
            # Применяем фильтр если указан
            if anomaly_type_filter and anomaly_type != anomaly_type_filter:
                continue
                
            for file_path in file_paths:
                if "::" in file_path:
                    archive_path, fb2_filename = file_path.split("::", 1)
                    files_to_check.append((archive_path, fb2_filename))

        return files_to_check

    def _analyze_quarantined_books(self, quarantined_books: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """Анализирует карантинные книги параллельно для поиска исправленных.

        Args:
            quarantined_books: Список книг из карантина

        Returns:
            Список книг, которые больше не имеют блокирующих аномалий
        """
        fixed_books = []

        def analyze_single_book(book: dict[str, Any]) -> Optional[dict[str, Any]]:
            """Анализирует одну книгу из карантина."""
            try:
                details = book.get("details", {})
                archive_path = details.get("archive_path")
                book_filename = details.get("book_filename")

                if not archive_path or not book_filename:
                    logger.debug(f"Пропуск {book['source_type']}:{book['source_id']} - нет путей")
                    return None

                # Читаем файл из архива
                try:
                    book_stream = self.storage_manager.read_file_from_archive(
                        archive_path, book_filename
                    )
                except Exception as e:
                    logger.debug(f"Не удалось прочитать {archive_path}::{book_filename}: {e}")
                    return None

                # Парсим в каноническую модель
                canonical_book = get_canonical_book_from_stream(
                    book_stream, book_filename, None, self.parser_dispatcher
                )

                # Получаем FB2 трансформер для проверки сносок
                fb2_transformer = get_fb2_transformer_from_parser(self.parser_dispatcher)

                # Проверяем на аномалии
                anomalies = self.book_validator.validate(canonical_book, fb2_transformer)

                # Если блокирующих аномалий нет - книга исправлена
                if not anomalies:
                    with self.stats_lock:
                        self.stats["anomalies_fixed"] += 1
                    return book

                return None

            except Exception as e:
                logger.debug(f"Ошибка анализа {book['source_type']}:{book['source_id']}: {e}")
                with self.stats_lock:
                    self.stats["errors"] += 1
                return None

        # Параллельный анализ
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_book = {
                executor.submit(analyze_single_book, book): book
                for book in quarantined_books
            }

            for future in as_completed(future_to_book):
                result = future.result()
                if result:
                    fixed_books.append(result)

                with self.stats_lock:
                    self.stats["processed_files"] += 1

        return fixed_books

    def _apply_fixes(self, fixed_books: list[dict[str, Any]]) -> int:
        """Применяет исправления: удаляет из карантина и ставит в очередь.

        Args:
            fixed_books: Список исправленных книг

        Returns:
            Количество успешно исправленных книг
        """
        success_count = 0

        for book in fixed_books:
            try:
                source_type = book["source_type"]
                source_id = book["source_id"]
                details = book.get("details", {})

                # Удаляем из карантина
                was_deleted = delete_from_quarantine(source_type, source_id)

                if was_deleted:
                    # Формируем task_data для постановки в очередь
                    task_data = {
                        "source_type": source_type,
                        "source_id": source_id,
                        "archive_path": details.get("archive_path"),
                        "book_filename": details.get("book_filename"),
                        "archive_mtime": details.get("archive_mtime", 0.0)
                    }

                    # Ставим в очередь парсинга
                    if self.queue_manager.enqueue_parsing_task(task_data):
                        success_count += 1
                        logger.debug(f"✅ Исправлено: {source_type}:{source_id}")
                    else:
                        logger.warning(f"⚠️ Удалено из карантина, но не поставлено в очередь: {source_type}:{source_id}")
                else:
                    logger.warning(f"⚠️ Книга не была в карантине: {source_type}:{source_id}")

            except Exception as e:
                logger.error(f"❌ Ошибка исправления {book['source_type']}:{book['source_id']}: {e}")

        return success_count

    def _recheck_anomaly_files(self, files_to_check: list[tuple[str, str]]) -> None:
        """Перепроверяет файлы из реестра аномалий.

        Args:
            files_to_check: Список кортежей (archive_path, fb2_filename)
        """
        def recheck_single_file(file_info: tuple[str, str]) -> None:
            """Перепроверяет один файл."""
            archive_path, fb2_filename = file_info

            try:
                # Читаем файл из архива
                try:
                    book_stream = self.storage_manager.read_file_from_archive(
                        archive_path, fb2_filename
                    )
                except Exception as e:
                    logger.debug(f"Не удалось прочитать {archive_path}::{fb2_filename}: {e}")
                    with self.stats_lock:
                        self.stats["errors"] += 1
                    return

                # Парсим в каноническую модель
                canonical_book = get_canonical_book_from_stream(
                    book_stream, fb2_filename, None, self.parser_dispatcher
                )

                # Получаем FB2 трансформер для проверки сносок
                fb2_transformer = get_fb2_transformer_from_parser(self.parser_dispatcher)

                # Проверяем на аномалии
                anomalies = self.book_validator.validate(canonical_book, fb2_transformer)

                # Обновляем реестр потокобезопасно
                with self.registry_lock:
                    # Удаляем старые записи для этого файла
                    for anomaly_type in self.anomaly_registry.registry_data["anomalies"]:
                        self.anomaly_registry.remove_anomaly_path(
                            anomaly_type, archive_path, fb2_filename
                        )

                    # Добавляем новые аномалии
                    for anomaly in anomalies:
                        anomaly_type = anomaly["type"]
                        self.anomaly_registry.add_anomaly_path(
                            anomaly_type, archive_path, fb2_filename
                        )

                        with self.stats_lock:
                            self.stats["anomalies_found"] += 1

                with self.stats_lock:
                    self.stats["processed_files"] += 1

            except Exception as e:
                logger.debug(f"Ошибка перепроверки {archive_path}::{fb2_filename}: {e}")
                with self.stats_lock:
                    self.stats["errors"] += 1

        # Параллельная перепроверка
        with ThreadPoolExecutor(max_workers=10) as executor:
            list(executor.map(recheck_single_file, files_to_check))

    def _print_stats(self) -> None:
        """Выводит статистику работы."""
        logger.info("📊 Статистика:")
        logger.info(f"  📁 Обработано файлов: {self.stats['processed_files']}")
        logger.info(f"  🔍 Найдено аномалий: {self.stats['anomalies_found']}")
        logger.info(f"  ✅ Исправлено книг: {self.stats['anomalies_fixed']}")
        logger.info(f"  ❌ Ошибок: {self.stats['errors']}")


def setup_logging(debug: bool = False) -> None:
    """Настраивает логирование для инструмента."""
    level = logging.DEBUG if debug else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )


def main() -> int:
    """Главная функция инструмента."""
    parser = argparse.ArgumentParser(
        description="Профессиональный инструмент для тюнинга детекторов аномалий",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Режимы работы:
  Разведка:    --path /p1 /p2  или  --from-file file.txt
  Тюнинг:      (без аргументов)  или  --type trial
  Исправление: --fix [--yes]

Примеры:
  %(prog)s                           # Тюнинг всех аномалий
  %(prog)s --type trial              # Тюнинг только trial аномалий  
  %(prog)s --path /data/books        # Разведка новых файлов
  %(prog)s --fix                     # Исправление карантинных книг
  %(prog)s --fix --yes               # Исправление без подтверждения
        """
    )
    
    # Режим Разведка
    parser.add_argument(
        "--path", 
        nargs="+", 
        help="Пути для сканирования новых файлов (режим Разведка)"
    )
    parser.add_argument(
        "--from-file", 
        help="Файл с путями для сканирования (режим Разведка)"
    )
    
    # Режим Тюнинг
    parser.add_argument(
        "--type", 
        help="Тип аномалий для фокусного тюнинга (trial, small_content, etc.)"
    )
    
    # Режим Исправление
    parser.add_argument(
        "--fix", 
        action="store_true", 
        help="Режим исправления карантинных книг"
    )
    parser.add_argument(
        "--yes", 
        action="store_true", 
        help="Пропустить интерактивное подтверждение для --fix"
    )
    
    # Общие опции
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Включить отладочный режим"
    )
    
    args = parser.parse_args()
    
    # Валидация аргументов
    if args.path and args.type:
        parser.error("--path несовместим с --type")
    if args.path and args.fix:
        parser.error("--path несовместим с --fix")
    if args.from_file and args.type:
        parser.error("--from-file несовместим с --type")
    if args.from_file and args.fix:
        parser.error("--from-file несовместим с --fix")
    if args.yes and not args.fix:
        parser.error("--yes работает только с --fix")
    
    # Настройка логирования
    setup_logging(args.debug)
    
    # Запуск оркестратора
    orchestrator = AnomalyOrchestrator()
    return orchestrator.run(args)


if __name__ == "__main__":
    sys.exit(main())
