#!/usr/bin/env python3
"""
Инструмент для создания статистических CSV-отчетов по книгам.

Этот инструмент выделен из run_anomaly_analyzer.py для фокусировки на
статистическом анализе без запуска детекторов аномалий. Он парсит книги
и извлекает только статистические поля для анализа.

Особенности:
- Облегченный парсинг: только статистические поля
- Не запускает BookValidator (детекторы аномалий)
- Создает CSV отчет с метаданными книг
- Поддерживает многопоточную обработку
"""

import argparse
import csv
import io
import logging
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Настройка базового логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%H:%M:%S')
logger = logging.getLogger(__name__)

# Импорты для статистического анализа
try:
    from app.processing.canonical_model import CanonicalBook
    from app.processing.date_extractor import extract_best_date
    from app.processing.parser_dispatcher import ParserDispatcher
    from app.storage import LocalStorageManager
    from tools.utils import get_canonical_book_from_stream, process_zip_archive
    FUNCTIONALITY_AVAILABLE = True
except ImportError as e:
    logger.error(f"❌ Необходимые компоненты недоступны: {e}")
    logger.error("🔧 Установите недостающие зависимости для работы инструмента")
    FUNCTIONALITY_AVAILABLE = False

# Колонки для статистического CSV отчета
REPORT_COLUMNS = [
    "archive_path",      # Путь к архиву
    "fb2_filename",      # Имя FB2 файла
    "title",             # Название книги
    "chapters_count",    # Количество глав
    "sequences_count",   # Количество серий
    "authors_count",     # Количество авторов
    "authors",           # Список авторов
    "sequences",         # Список серий
    "lang",              # Язык книги
    "date_source",       # Источник даты для book_id
    "generation_date",   # Дата генерации book_id
    "genres",            # Жанры
    "has_annotation",    # Есть ли аннотация
]


def extract_statistical_data(
    canonical_book: CanonicalBook,
    archive_path: str,
    fb2_filename: str,
) -> dict[str, Any]:
    """Извлекает статистические данные из книги для отчета.
    
    Args:
        canonical_book: Каноническая модель книги
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла
        
    Returns:
        Словарь со статистическими данными
    """
    # Формируем строку авторов
    authors_list = []
    for author in canonical_book.authors:
        name_parts = [author.first_name, author.middle_name, author.last_name]
        full_name = " ".join(filter(None, name_parts))
        if full_name:
            authors_list.append(full_name)
    authors_str = "; ".join(authors_list) if authors_list else "Unknown"

    # Формируем строку серий
    sequences_list = []
    for seq in canonical_book.sequences:
        seq_str = seq.name
        if seq.number is not None:
            seq_str += f" #{seq.number}"
        sequences_list.append(seq_str)
    sequences_str = "; ".join(sequences_list) if sequences_list else ""

    # Извлекаем дату генерации и источник даты
    date_source = "unknown"
    generation_date = "unknown"

    if canonical_book.raw_source_model is not None:
        try:
            best_date, date_source = extract_best_date(canonical_book.raw_source_model, None)
            generation_date = best_date.strftime("%Y-%m-%d")
        except Exception:
            pass

    return {
        "archive_path": archive_path,
        "fb2_filename": fb2_filename,
        "title": canonical_book.title or "No Title",
        "chapters_count": len(canonical_book.chapters),
        "sequences_count": len(canonical_book.sequences),
        "authors_count": len(canonical_book.authors),
        "authors": authors_str,
        "sequences": sequences_str,
        "lang": canonical_book.lang or "unknown",
        "date_source": date_source,
        "generation_date": generation_date,
        "genres": "; ".join(canonical_book.genres) if canonical_book.genres else "",
        "has_annotation": "Yes" if canonical_book.annotation_md.strip() else "No",
    }


class StatisticalReporter:
    """Генератор статистических отчетов по книгам."""

    def __init__(self, output_path: Path, max_workers: int = 10):
        """Инициализирует репортер.
        
        Args:
            output_path: Путь к выходному CSV файлу
            max_workers: Количество потоков для обработки
        """
        if not FUNCTIONALITY_AVAILABLE:
            raise RuntimeError("Необходимые компоненты недоступны")
            
        self.output_path = output_path
        self.max_workers = max_workers
        
        # Компоненты
        self.parser_dispatcher = ParserDispatcher()
        self.storage_manager = LocalStorageManager()
        
        # Блокировки для потокобезопасности
        self.csv_lock = threading.Lock()
        self.stats_lock = threading.Lock()
        
        # Статистика
        self.stats = {
            "processed_files": 0,
            "processed_archives": 0,
            "errors": 0,
            "start_time": datetime.now()
        }
        
        # CSV файл
        self.csv_file: Optional[io.TextIOWrapper] = None
        self.csv_writer: Optional[csv.writer] = None

    def __enter__(self):
        """Контекстный менеджер - открывает CSV файл."""
        self.csv_file = open(self.output_path, "w", encoding="utf-8-sig", newline="")
        self.csv_writer = csv.writer(self.csv_file, delimiter=";")
        self.csv_writer.writerow(REPORT_COLUMNS)
        self.csv_file.flush()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Контекстный менеджер - закрывает CSV файл."""
        if self.csv_file:
            self.csv_file.close()

    def process_paths(self, paths: list[str]) -> None:
        """Обрабатывает указанные пути и создает статистический отчет.
        
        Args:
            paths: Список путей к архивам или директориям
        """
        # Собираем архивы для обработки
        archives_to_process = []
        for path_str in paths:
            path = Path(path_str)
            if path.is_file() and path.suffix.lower() == '.zip':
                archives_to_process.append(path)
            elif path.is_dir():
                zip_files = list(path.glob("*.zip"))
                archives_to_process.extend(zip_files)
                
        if not archives_to_process:
            logger.warning("⚠️ Не найдено ZIP архивов для обработки")
            return
            
        logger.info(f"📦 Найдено {len(archives_to_process)} архивов для обработки")
        
        # Обрабатываем архивы параллельно
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_path = {
                executor.submit(self._process_single_archive, archive_path): archive_path 
                for archive_path in archives_to_process
            }
            
            for future in as_completed(future_to_path):
                archive_path = future_to_path[future]
                try:
                    future.result()
                    with self.stats_lock:
                        self.stats["processed_archives"] += 1
                except Exception as e:
                    logger.error(f"❌ Ошибка обработки архива {archive_path}: {e}")
                    with self.stats_lock:
                        self.stats["errors"] += 1

    def _process_single_archive(self, archive_path: Path) -> None:
        """Обрабатывает один архив.
        
        Args:
            archive_path: Путь к ZIP архиву
        """
        def fb2_processor(
            archive_path: str,
            fb2_filename: str,
            fb2_stream: io.BytesIO,
            file_mtime: Optional[float],
        ) -> None:
            """Обрабатывает один FB2 файл."""
            try:
                # Парсим книгу из потока
                canonical_book = get_canonical_book_from_stream(
                    fb2_stream, fb2_filename, file_mtime, self.parser_dispatcher
                )
                
                # Извлекаем статистические данные
                book_data = extract_statistical_data(
                    canonical_book, archive_path, fb2_filename
                )
                
                # Записываем в CSV потокобезопасно
                with self.csv_lock:
                    if self.csv_writer:
                        row = [book_data.get(col, "") for col in REPORT_COLUMNS]
                        self.csv_writer.writerow(row)
                        self.csv_file.flush()
                
                with self.stats_lock:
                    self.stats["processed_files"] += 1
                    
            except Exception as e:
                logger.debug(f"Ошибка обработки {archive_path}::{fb2_filename}: {e}")
                with self.stats_lock:
                    self.stats["errors"] += 1

        def error_handler(archive_path: str, fb2_filename: str, exc: Exception) -> None:
            """Обработчик ошибок архива."""
            logger.debug(f"Ошибка архива {archive_path}: {exc}")
            with self.stats_lock:
                self.stats["errors"] += 1

        # Обрабатываем архив через утилитарную функцию
        process_zip_archive(
            zip_path=archive_path,
            fb2_processor=fb2_processor,
            fb2_filter=lambda filename: filename.lower().endswith('.fb2'),
            error_handler=error_handler,
        )

    def print_stats(self) -> None:
        """Выводит статистику обработки."""
        end_time = datetime.now()
        total_time = (end_time - self.stats["start_time"]).total_seconds()
        
        logger.info("📊 Статистика обработки:")
        logger.info(f"  📦 Архивов обработано: {self.stats['processed_archives']}")
        logger.info(f"  📚 Книг обработано: {self.stats['processed_files']}")
        logger.info(f"  ❌ Ошибок: {self.stats['errors']}")
        logger.info(f"  ⏱️ Время: {total_time:.1f} сек")
        
        if total_time > 0:
            speed = self.stats["processed_files"] / total_time
            logger.info(f"  🚀 Скорость: {speed:.2f} книг/сек")


def main() -> int:
    """Главная функция инструмента."""
    parser = argparse.ArgumentParser(
        description="Инструмент для создания статистических CSV-отчетов по книгам",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s --path /data/books --output report.csv
  %(prog)s --path /archive1.zip /archive2.zip --output analysis.csv
  %(prog)s --path /books --output stats.csv --workers 20
        """
    )
    
    parser.add_argument(
        "--path",
        nargs="+",
        required=True,
        help="Пути к архивам или директориям для анализа"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Путь к выходному CSV файлу"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=10,
        help="Количество потоков для обработки (по умолчанию: 10)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Включить отладочный режим"
    )
    
    args = parser.parse_args()
    
    # Настройка логирования
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Проверка доступности функциональности
    if not FUNCTIONALITY_AVAILABLE:
        logger.error("❌ Необходимые компоненты недоступны")
        return 1
    
    output_path = Path(args.output)
    
    # Создаем директорию если не существует
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"📊 Создание статистического отчета: {output_path}")
    logger.info(f"📁 Обработка {len(args.path)} путей с {args.workers} потоками")
    
    try:
        with StatisticalReporter(output_path, args.workers) as reporter:
            reporter.process_paths(args.path)
            reporter.print_stats()
            
        logger.info(f"✅ Отчет создан: {output_path}")
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 Прервано пользователем")
        return 1
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
